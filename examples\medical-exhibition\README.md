# 医疗健康展厅示例应用

## 概述

这是一个基于视觉脚本系统构建的完整医疗健康展厅应用，展示了如何使用我们的294个视觉脚本节点创建沉浸式的教育和交互体验。

## 功能特性

### 🤖 智能数字人交互
- **主动打招呼**: 数字人检测到用户进入时主动问候
- **自然语音对话**: 支持连续语音识别和自然语言理解
- **情感表达**: 根据对话内容展现不同的情感状态
- **动画控制**: 丰富的身体语言和面部表情

### 🎤 高级语音交互
- **实时语音识别**: 支持中文语音识别，准确率高
- **语音合成**: 自然流畅的语音回复
- **语音活动检测**: 智能检测用户说话状态
- **多轮对话管理**: 维护对话上下文和历史

### 📚 智能知识库
- **医疗设备信息**: 包含详细的设备规格、使用方法、安全指南
- **智能搜索**: 支持语义搜索和模糊匹配
- **问答系统**: 基于知识库的智能问答
- **相关推荐**: 自动推荐相关设备和信息

### 🎨 丰富的UI交互
- **响应式界面**: 适配不同屏幕尺寸
- **实时信息显示**: 动态更新设备信息和状态
- **交互式控件**: 按钮、面板、列表等UI组件
- **主题切换**: 支持多种界面主题

## 技术架构

### 视觉脚本节点使用统计

本应用充分利用了视觉脚本系统的294个节点：

#### 核心节点 (14个)
- `OnStartNode`: 应用启动初始化
- `OnUpdateNode`: 实时状态更新
- `SequenceNode`: 流程控制
- `BranchNode`: 条件分支
- `DelayNode`: 延迟控制

#### AI交互节点 (42个)
- **自然语言处理 (14个)**:
  - `SpeechRecognitionNode`: 语音识别
  - `SpeechSynthesisNode`: 语音合成
  - `DialogueManagementNode`: 对话管理
  - `QuestionAnsweringNode`: 问答处理
  - `IntentRecognitionNode`: 意图识别

- **情感分析 (8个)**:
  - `EmotionAnalysisNode`: 情感分析
  - `EmotionDrivenAnimationNode`: 情感驱动动画
  - `EmotionTransitionNode`: 情感过渡

- **AI模型 (12个)**:
  - `LoadAIModelNode`: 模型加载
  - `TextGenerationNode`: 文本生成
  - `EmotionAnalysisNode`: 情感分析

#### UI交互节点 (20个)
- `CreateButtonNode`: 创建按钮
- `CreateTextNode`: 文本显示
- `CreatePanelNode`: 面板创建
- `UIEventListenerNode`: 事件监听

#### 音频处理节点 (13个)
- `PlayAudioNode`: 音频播放
- `Audio3DNode`: 3D空间音频
- `AudioAnalyzerNode`: 音频分析

#### 网络通信节点 (30个)
- `HTTPGetNode`: HTTP请求
- `WebRTCNodes`: 实时通信
- `NetworkNodes`: 网络连接

### 系统组件

```typescript
// 主要系统组件
- MedicalExhibitionApp: 主应用类
- DigitalHumanSystem: 数字人交互系统
- VoiceInteractionSystem: 语音交互系统
- MedicalKnowledgeBase: 医疗知识库系统
```

## 快速开始

### 安装依赖

```bash
npm install
```

### 运行演示

```bash
# 运行完整演示
npm run demo:medical-exhibition

# 或者直接运行TypeScript文件
npx ts-node examples/medical-exhibition/demo.ts
```

### 基本使用

```typescript
import { MedicalExhibitionApp } from './src/MedicalExhibitionApp';

// 创建应用配置
const config = {
  scene: {
    name: '智慧医疗展厅',
    description: '展示最新医疗设备的学习环境',
    environment: 'modern_hospital'
  },
  digitalHuman: {
    name: '小医',
    personality: {
      friendliness: 0.9,
      professionalism: 0.95
    }
  },
  // ... 其他配置
};

// 创建并启动应用
const app = new MedicalExhibitionApp(config);
await app.start();

// 模拟用户进入
await app.onUserEnter('user_001', '张医生');
```

## 演示场景

### 场景1: 用户进入展厅
- 用户虚拟化身进入3D场景
- 数字人主动检测并打招呼
- 显示欢迎界面和设备列表
- 开始语音交互监听

### 场景2: 设备咨询
- 用户语音询问："MRI是什么设备？"
- 语音识别转换为文本
- 知识库智能检索相关信息
- 数字人语音回复并展示情感

### 场景3: 设备搜索
- 用户搜索特定类型设备
- 系统返回相关度排序的结果
- 显示设备缩略图和基本信息
- 支持进一步筛选和排序

### 场景4: 详细信息
- 用户选择感兴趣的设备
- 显示详细技术规格
- 播放演示视频
- 展示3D模型交互

### 场景5: 用户离开
- 数字人友好告别
- 保存用户学习记录
- 清理会话资源

## 核心特性展示

### 🎯 多模态交互
- **语音交互**: 自然语言对话
- **视觉交互**: 3D场景和UI界面
- **触控交互**: 点击、拖拽、缩放
- **手势识别**: 支持手势控制

### 🧠 智能化功能
- **意图理解**: 准确识别用户意图
- **上下文记忆**: 维护对话上下文
- **个性化推荐**: 基于用户兴趣推荐
- **自适应学习**: 根据用户反馈优化

### 🎨 沉浸式体验
- **3D环境**: 逼真的医院环境
- **实时渲染**: 高质量图形渲染
- **空间音频**: 3D立体声效果
- **物理交互**: 真实的物理反馈

### 📊 数据分析
- **学习分析**: 跟踪学习进度
- **行为分析**: 分析用户行为模式
- **性能监控**: 实时性能指标
- **使用统计**: 详细的使用报告

## 技术亮点

### 视觉脚本系统集成
- 充分利用294个节点的强大功能
- 模块化的节点组合和复用
- 可视化的逻辑流程设计
- 实时的脚本执行和调试

### AI技术应用
- 先进的自然语言处理
- 实时情感分析和表达
- 智能知识检索和问答
- 个性化交互体验

### 性能优化
- 高效的资源管理
- 智能缓存机制
- 异步处理优化
- 内存使用优化

## 扩展性

### 添加新设备
```typescript
// 添加新的医疗设备到知识库
const newEquipment = {
  id: 'ultrasound-001',
  name: '超声诊断仪',
  category: EquipmentCategory.DIAGNOSTIC,
  // ... 其他属性
};

app.knowledgeBase.addEquipment(newEquipment);
```

### 自定义数字人
```typescript
// 自定义数字人外观和性格
const customDigitalHuman = {
  name: '专家医生',
  appearance: {
    model: 'expert_doctor.glb',
    // ...
  },
  personality: {
    professionalism: 1.0,
    expertise: 0.95
  }
};
```

### 添加新的交互方式
```typescript
// 扩展新的输入方式
class GestureInputSystem {
  // 手势识别实现
}

// 集成到主应用
app.addInputSystem(new GestureInputSystem());
```

## 部署说明

### 开发环境
- Node.js 16+
- TypeScript 4.5+
- WebGL支持的浏览器

### 生产环境
- 支持WebRTC的现代浏览器
- 稳定的网络连接
- 推荐使用HTTPS协议

### 性能要求
- 最低4GB内存
- 支持WebGL 2.0
- 推荐使用独立显卡

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个示例应用。

## 联系方式

如有问题或建议，请联系开发团队。
